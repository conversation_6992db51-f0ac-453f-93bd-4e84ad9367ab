<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Australian Wealth Tracker</title>
    <link rel="stylesheet" href="styles.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
</head>
<body>
    <div class="container">
        <header>
            <h1>🇦🇺 Australian Wealth Tracker</h1>
            <div class="financial-year-info">
                <div class="fy-display">
                    <span id="current-fy">FY 2024-25</span>
                    <span id="days-remaining">Days until FY end: <strong id="days-count">0</strong></span>
                </div>
            </div>
        </header>

        <main>
            <!-- Salary Section -->
            <section class="card salary-section">
                <h2>💰 Annual Salary (Before Tax)</h2>
                <div class="salary-input-group">
                    <div class="input-wrapper">
                        <label for="annual-salary">Annual Salary (AUD)</label>
                        <input type="number" id="annual-salary" placeholder="e.g., 85000" min="0" step="1000">
                    </div>
                    <button id="save-salary" class="btn btn-primary">Save Salary</button>
                </div>
                <div class="salary-summary" id="salary-summary" style="display: none;">
                    <div class="tax-breakdown">
                        <h3>Tax Breakdown (2024-25)</h3>
                        <div class="tax-item">
                            <span>Gross Salary:</span>
                            <span id="gross-salary">$0</span>
                        </div>
                        <div class="tax-item">
                            <span>Income Tax:</span>
                            <span id="income-tax">$0</span>
                        </div>
                        <div class="tax-item">
                            <span>Medicare Levy (2%):</span>
                            <span id="medicare-levy">$0</span>
                        </div>
                        <div class="tax-item total">
                            <span>Net Salary:</span>
                            <span id="net-salary">$0</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- File Upload Section -->
            <section class="card upload-section">
                <h2>📊 Stock Transactions</h2>
                <div class="upload-area">
                    <div class="upload-zone" id="upload-zone">
                        <div class="upload-icon">📁</div>
                        <p>Drag & drop your CSV/XLSX file here or click to browse</p>
                        <p class="upload-hint">Expected columns: Date, Confirmation No., Code, Quantity, Action, Avg. price, Fees, Settl. value</p>
                        <input type="file" id="file-input" accept=".csv,.xlsx,.xls" hidden>
                    </div>
                    <button id="browse-file" class="btn btn-secondary">Browse Files</button>
                </div>
                <div class="file-status" id="file-status"></div>
            </section>

            <!-- Portfolio Overview -->
            <section class="card portfolio-overview">
                <h2>📈 Portfolio Overview</h2>
                <div class="portfolio-stats">
                    <div class="stat-card">
                        <h3>Total Value</h3>
                        <span class="stat-value" id="total-value">$0.00</span>
                    </div>
                    <div class="stat-card">
                        <h3>Total Gains/Losses</h3>
                        <span class="stat-value" id="total-gains">$0.00</span>
                    </div>
                    <div class="stat-card">
                        <h3>Percentage Change</h3>
                        <span class="stat-value" id="percentage-change">0.00%</span>
                    </div>
                    <div class="stat-card">
                        <h3>Holdings Count</h3>
                        <span class="stat-value" id="holdings-count">0</span>
                    </div>
                </div>
            </section>

            <!-- Portfolio Visualization -->
            <section class="card chart-section">
                <h2>🥧 Portfolio Allocation</h2>
                <div class="chart-container">
                    <canvas id="portfolio-chart"></canvas>
                </div>
                <div class="chart-legend" id="chart-legend"></div>
            </section>

            <!-- Transaction History -->
            <section class="card transactions-section">
                <h2>📋 Transaction History</h2>
                <div class="table-controls">
                    <div class="search-box">
                        <input type="text" id="search-transactions" placeholder="Search by stock code...">
                    </div>
                    <div class="filter-controls">
                        <select id="action-filter">
                            <option value="">All Actions</option>
                            <option value="BUY">Buy</option>
                            <option value="SELL">Sell</option>
                        </select>
                        <button id="clear-data" class="btn btn-danger">Clear All Data</button>
                    </div>
                </div>
                <div class="table-wrapper">
                    <table id="transactions-table">
                        <thead>
                            <tr>
                                <th data-sort="date">Date</th>
                                <th data-sort="code">Code</th>
                                <th data-sort="action">Action</th>
                                <th data-sort="quantity">Quantity</th>
                                <th data-sort="price">Avg. Price</th>
                                <th data-sort="fees">Fees</th>
                                <th data-sort="value">Settlement Value</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="transactions-tbody">
                            <tr class="no-data">
                                <td colspan="8">No transactions loaded. Upload a file to get started.</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- Holdings Summary -->
            <section class="card holdings-section">
                <h2>🏦 Current Holdings</h2>
                <div class="table-wrapper">
                    <table id="holdings-table">
                        <thead>
                            <tr>
                                <th>Stock Code</th>
                                <th>Quantity</th>
                                <th>Avg. Cost</th>
                                <th>Current Value</th>
                                <th>Unrealized P&L</th>
                                <th>% Change</th>
                                <th>CGT Status</th>
                            </tr>
                        </thead>
                        <tbody id="holdings-tbody">
                            <tr class="no-data">
                                <td colspan="7">No holdings to display.</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </section>
        </main>

        <footer>
            <p>&copy; 2024 Australian Wealth Tracker | Optimized for Australian Tax Year (July 1 - June 30)</p>
            <p class="disclaimer">This tool is for informational purposes only. Consult a qualified financial advisor for investment advice.</p>
        </footer>
    </div>

    <script src="script.js"></script>
</body>
</html>
