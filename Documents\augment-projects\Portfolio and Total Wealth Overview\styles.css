/* Australian Wealth Tracker Styles */
:root {
    --primary-color: #00843d; /* Australian green */
    --secondary-color: #ffcd00; /* Australian gold */
    --accent-color: #0066cc;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --light-bg: #f8f9fa;
    --dark-text: #212529;
    --border-color: #dee2e6;
    --shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--dark-text);
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header Styles */
header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

header h1 {
    color: var(--primary-color);
    font-size: 2.5rem;
    margin-bottom: 15px;
}

.financial-year-info {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 15px;
    border-radius: var(--border-radius);
    margin-top: 15px;
}

.fy-display {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
}

/* Card Styles */
.card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 25px;
    margin-bottom: 25px;
    border-left: 4px solid var(--primary-color);
}

.card h2 {
    color: var(--primary-color);
    margin-bottom: 20px;
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

/* Button Styles */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: #006633;
    transform: translateY(-2px);
}

.btn-secondary {
    background: var(--accent-color);
    color: white;
}

.btn-secondary:hover {
    background: #0056b3;
}

.btn-danger {
    background: var(--danger-color);
    color: white;
}

.btn-danger:hover {
    background: #c82333;
}

/* Form Styles */
.salary-input-group {
    display: flex;
    gap: 15px;
    align-items: end;
    flex-wrap: wrap;
}

.input-wrapper {
    flex: 1;
    min-width: 200px;
}

.input-wrapper label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: var(--dark-text);
}

input[type="number"],
input[type="text"],
select {
    width: 100%;
    padding: 12px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

input[type="number"]:focus,
input[type="text"]:focus,
select:focus {
    outline: none;
    border-color: var(--primary-color);
}

/* Salary Summary */
.salary-summary {
    margin-top: 20px;
    padding: 20px;
    background: var(--light-bg);
    border-radius: var(--border-radius);
}

.tax-breakdown h3 {
    color: var(--primary-color);
    margin-bottom: 15px;
}

.tax-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid var(--border-color);
}

.tax-item.total {
    font-weight: bold;
    font-size: 1.1rem;
    border-bottom: 2px solid var(--primary-color);
    margin-top: 10px;
}

/* Upload Section */
.upload-area {
    text-align: center;
}

.upload-zone {
    border: 3px dashed var(--border-color);
    border-radius: var(--border-radius);
    padding: 40px 20px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-zone:hover,
.upload-zone.dragover {
    border-color: var(--primary-color);
    background: var(--light-bg);
}

.upload-icon {
    font-size: 3rem;
    margin-bottom: 15px;
}

.upload-hint {
    font-size: 0.9rem;
    color: #666;
    margin-top: 10px;
}

.file-status {
    margin-top: 15px;
    padding: 10px;
    border-radius: var(--border-radius);
    display: none;
}

.file-status.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.file-status.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Portfolio Stats */
.portfolio-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.stat-card {
    background: var(--light-bg);
    padding: 20px;
    border-radius: var(--border-radius);
    text-align: center;
    border: 1px solid var(--border-color);
}

.stat-card h3 {
    color: var(--dark-text);
    font-size: 0.9rem;
    margin-bottom: 10px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.stat-value {
    font-size: 1.8rem;
    font-weight: bold;
    color: var(--primary-color);
}

.stat-value.positive {
    color: var(--success-color);
}

.stat-value.negative {
    color: var(--danger-color);
}

/* Chart Section */
.chart-container {
    position: relative;
    height: 400px;
    margin-bottom: 20px;
}

.chart-legend {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    justify-content: center;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
}

.legend-color {
    width: 16px;
    height: 16px;
    border-radius: 50%;
}

/* Table Styles */
.table-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
}

.search-box input {
    min-width: 250px;
}

.filter-controls {
    display: flex;
    gap: 10px;
    align-items: center;
}

.table-wrapper {
    overflow-x: auto;
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

th, td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

th {
    background: var(--light-bg);
    font-weight: 600;
    color: var(--dark-text);
    cursor: pointer;
    user-select: none;
}

th:hover {
    background: #e9ecef;
}

th[data-sort]::after {
    content: ' ↕️';
    font-size: 0.8rem;
}

tr:hover {
    background: var(--light-bg);
}

.no-data {
    text-align: center;
    color: #666;
    font-style: italic;
}

/* Footer */
footer {
    text-align: center;
    padding: 20px;
    color: #666;
    border-top: 1px solid var(--border-color);
    margin-top: 40px;
}

.disclaimer {
    font-size: 0.8rem;
    margin-top: 10px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .fy-display {
        flex-direction: column;
        text-align: center;
    }
    
    .salary-input-group {
        flex-direction: column;
    }
    
    .table-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-controls {
        justify-content: space-between;
    }
    
    .portfolio-stats {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .portfolio-stats {
        grid-template-columns: 1fr;
    }
    
    .chart-container {
        height: 300px;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}
