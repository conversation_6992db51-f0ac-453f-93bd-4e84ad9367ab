// Australian Wealth Tracker - Main JavaScript File

class AustralianWealthTracker {
    constructor() {
        this.data = {
            salary: 0,
            transactions: [],
            holdings: new Map(),
            lastUpdated: null
        };
        
        this.chart = null;
        this.currentSort = { column: 'date', direction: 'desc' };
        
        this.init();
    }

    init() {
        this.loadData();
        this.setupEventListeners();
        this.updateFinancialYear();
        this.updatePortfolioDisplay();
        this.updateSalaryDisplay();
        
        // Update financial year countdown every hour
        setInterval(() => this.updateFinancialYear(), 3600000);
    }

    // Australian Financial Year Calculations
    updateFinancialYear() {
        const now = new Date();
        const currentYear = now.getFullYear();
        
        // Australian FY runs July 1 - June 30
        let fyEndYear = currentYear;
        if (now.getMonth() >= 6) { // July onwards
            fyEndYear = currentYear + 1;
        }
        
        const fyEnd = new Date(fyEndYear, 5, 30); // June 30
        const fyStart = new Date(fyEndYear - 1, 6, 1); // July 1
        
        // Calculate days remaining
        const msPerDay = 24 * 60 * 60 * 1000;
        const daysRemaining = Math.ceil((fyEnd - now) / msPerDay);
        
        // Update display
        document.getElementById('current-fy').textContent = `FY ${fyEndYear - 1}-${fyEndYear.toString().slice(-2)}`;
        document.getElementById('days-count').textContent = Math.max(0, daysRemaining);
    }

    // Australian Tax Calculations (2024-25 rates)
    calculateAustralianTax(grossSalary) {
        let tax = 0;
        
        // 2024-25 Australian tax brackets
        if (grossSalary > 190000) {
            tax += (grossSalary - 190000) * 0.45;
            tax += (190000 - 120000) * 0.37;
            tax += (120000 - 45000) * 0.325;
            tax += (45000 - 18200) * 0.19;
        } else if (grossSalary > 120000) {
            tax += (grossSalary - 120000) * 0.37;
            tax += (120000 - 45000) * 0.325;
            tax += (45000 - 18200) * 0.19;
        } else if (grossSalary > 45000) {
            tax += (grossSalary - 45000) * 0.325;
            tax += (45000 - 18200) * 0.19;
        } else if (grossSalary > 18200) {
            tax += (grossSalary - 18200) * 0.19;
        }
        
        // Medicare levy (2%)
        const medicareLevy = grossSalary > 24276 ? grossSalary * 0.02 : 0;
        
        return {
            incomeTax: Math.round(tax),
            medicareLevy: Math.round(medicareLevy),
            totalTax: Math.round(tax + medicareLevy),
            netSalary: Math.round(grossSalary - tax - medicareLevy)
        };
    }

    // Event Listeners Setup
    setupEventListeners() {
        // Salary management
        document.getElementById('save-salary').addEventListener('click', () => this.saveSalary());
        document.getElementById('annual-salary').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.saveSalary();
        });

        // File upload
        const fileInput = document.getElementById('file-input');
        const uploadZone = document.getElementById('upload-zone');
        const browseBtn = document.getElementById('browse-file');

        browseBtn.addEventListener('click', () => fileInput.click());
        fileInput.addEventListener('change', (e) => this.handleFileUpload(e.target.files[0]));

        // Drag and drop
        uploadZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadZone.classList.add('dragover');
        });

        uploadZone.addEventListener('dragleave', () => {
            uploadZone.classList.remove('dragover');
        });

        uploadZone.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadZone.classList.remove('dragover');
            this.handleFileUpload(e.dataTransfer.files[0]);
        });

        uploadZone.addEventListener('click', () => fileInput.click());

        // Table controls
        document.getElementById('search-transactions').addEventListener('input', (e) => {
            this.filterTransactions(e.target.value);
        });

        document.getElementById('action-filter').addEventListener('change', (e) => {
            this.filterTransactions(document.getElementById('search-transactions').value, e.target.value);
        });

        document.getElementById('clear-data').addEventListener('click', () => {
            if (confirm('Are you sure you want to clear all data? This action cannot be undone.')) {
                this.clearAllData();
            }
        });

        // Table sorting
        document.querySelectorAll('th[data-sort]').forEach(th => {
            th.addEventListener('click', () => this.sortTable(th.dataset.sort));
        });
    }

    // Salary Management
    saveSalary() {
        const salaryInput = document.getElementById('annual-salary');
        const salary = parseFloat(salaryInput.value);
        
        if (isNaN(salary) || salary < 0) {
            this.showStatus('Please enter a valid salary amount.', 'error');
            return;
        }
        
        this.data.salary = salary;
        this.saveData();
        this.updateSalaryDisplay();
        this.showStatus('Salary saved successfully!', 'success');
    }

    updateSalaryDisplay() {
        const salary = this.data.salary;
        const summaryDiv = document.getElementById('salary-summary');
        
        if (salary > 0) {
            const taxCalc = this.calculateAustralianTax(salary);
            
            document.getElementById('gross-salary').textContent = this.formatCurrency(salary);
            document.getElementById('income-tax').textContent = this.formatCurrency(taxCalc.incomeTax);
            document.getElementById('medicare-levy').textContent = this.formatCurrency(taxCalc.medicareLevy);
            document.getElementById('net-salary').textContent = this.formatCurrency(taxCalc.netSalary);
            
            summaryDiv.style.display = 'block';
            summaryDiv.classList.add('fade-in');
        } else {
            summaryDiv.style.display = 'none';
        }
    }

    // File Upload and Processing
    async handleFileUpload(file) {
        if (!file) return;
        
        const statusDiv = document.getElementById('file-status');
        statusDiv.style.display = 'block';
        statusDiv.className = 'file-status loading';
        statusDiv.textContent = 'Processing file...';
        
        try {
            let data;
            
            if (file.name.endsWith('.csv')) {
                data = await this.parseCSV(file);
            } else if (file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
                data = await this.parseExcel(file);
            } else {
                throw new Error('Unsupported file format. Please use CSV or Excel files.');
            }
            
            this.processTransactions(data);
            statusDiv.className = 'file-status success';
            statusDiv.textContent = `Successfully loaded ${data.length} transactions from ${file.name}`;
            
        } catch (error) {
            statusDiv.className = 'file-status error';
            statusDiv.textContent = `Error: ${error.message}`;
        }
    }

    async parseCSV(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const csv = e.target.result;
                    const lines = csv.split('\n');
                    const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
                    
                    const data = [];
                    for (let i = 1; i < lines.length; i++) {
                        if (lines[i].trim()) {
                            const values = this.parseCSVLine(lines[i]);
                            const row = {};
                            headers.forEach((header, index) => {
                                row[header] = values[index] || '';
                            });
                            data.push(row);
                        }
                    }
                    resolve(data);
                } catch (error) {
                    reject(new Error('Failed to parse CSV file: ' + error.message));
                }
            };
            reader.onerror = () => reject(new Error('Failed to read file'));
            reader.readAsText(file);
        });
    }

    parseCSVLine(line) {
        const result = [];
        let current = '';
        let inQuotes = false;
        
        for (let i = 0; i < line.length; i++) {
            const char = line[i];
            
            if (char === '"') {
                inQuotes = !inQuotes;
            } else if (char === ',' && !inQuotes) {
                result.push(current.trim());
                current = '';
            } else {
                current += char;
            }
        }
        
        result.push(current.trim());
        return result;
    }

    async parseExcel(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const data = new Uint8Array(e.target.result);
                    const workbook = XLSX.read(data, { type: 'array' });
                    const sheetName = workbook.SheetNames[0];
                    const worksheet = workbook.Sheets[sheetName];
                    const jsonData = XLSX.utils.sheet_to_json(worksheet);
                    resolve(jsonData);
                } catch (error) {
                    reject(new Error('Failed to parse Excel file: ' + error.message));
                }
            };
            reader.onerror = () => reject(new Error('Failed to read file'));
            reader.readAsArrayBuffer(file);
        });
    }

    // Data Management
    saveData() {
        try {
            this.data.lastUpdated = new Date().toISOString();
            localStorage.setItem('australianWealthTracker', JSON.stringify(this.data));
        } catch (error) {
            console.error('Failed to save data:', error);
            this.showStatus('Failed to save data to local storage.', 'error');
        }
    }

    loadData() {
        try {
            const saved = localStorage.getItem('australianWealthTracker');
            if (saved) {
                this.data = { ...this.data, ...JSON.parse(saved) };
                // Convert holdings back to Map
                if (this.data.holdings && !(this.data.holdings instanceof Map)) {
                    this.data.holdings = new Map(Object.entries(this.data.holdings));
                }
            }
        } catch (error) {
            console.error('Failed to load data:', error);
            this.showStatus('Failed to load saved data.', 'error');
        }
    }

    clearAllData() {
        this.data = {
            salary: 0,
            transactions: [],
            holdings: new Map(),
            lastUpdated: null
        };
        
        localStorage.removeItem('australianWealthTracker');
        
        // Reset UI
        document.getElementById('annual-salary').value = '';
        document.getElementById('file-status').style.display = 'none';
        
        this.updateSalaryDisplay();
        this.updatePortfolioDisplay();
        this.updateTransactionsTable();
        this.updateHoldingsTable();
        
        this.showStatus('All data cleared successfully.', 'success');
    }

    // Utility Functions
    formatCurrency(amount) {
        return new Intl.NumberFormat('en-AU', {
            style: 'currency',
            currency: 'AUD'
        }).format(amount);
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-AU');
    }

    showStatus(message, type) {
        // Create a temporary status message
        const statusDiv = document.createElement('div');
        statusDiv.className = `file-status ${type}`;
        statusDiv.textContent = message;
        statusDiv.style.position = 'fixed';
        statusDiv.style.top = '20px';
        statusDiv.style.right = '20px';
        statusDiv.style.zIndex = '1000';
        statusDiv.style.maxWidth = '300px';
        
        document.body.appendChild(statusDiv);
        
        setTimeout(() => {
            statusDiv.remove();
        }, 3000);
    }

    updatePortfolioDisplay() {
        const holdings = Array.from(this.data.holdings.values());

        // Calculate portfolio totals
        let totalValue = 0;
        let totalCost = 0;

        for (const holding of holdings) {
            // For demo purposes, use average cost as current price
            // In a real app, you'd fetch current market prices
            const currentPrice = holding.avgCost * (0.95 + Math.random() * 0.1); // Simulate price movement
            const currentValue = holding.quantity * currentPrice;

            holding.currentPrice = currentPrice;
            holding.currentValue = currentValue;
            holding.unrealizedPL = currentValue - holding.totalCost;
            holding.percentChange = holding.totalCost > 0 ? (holding.unrealizedPL / holding.totalCost) * 100 : 0;

            totalValue += currentValue;
            totalCost += holding.totalCost;
        }

        const totalGains = totalValue - totalCost;
        const percentageChange = totalCost > 0 ? (totalGains / totalCost) * 100 : 0;

        // Update portfolio stats
        document.getElementById('total-value').textContent = this.formatCurrency(totalValue);
        document.getElementById('total-gains').textContent = this.formatCurrency(totalGains);
        document.getElementById('percentage-change').textContent = percentageChange.toFixed(2) + '%';
        document.getElementById('holdings-count').textContent = holdings.length;

        // Apply color classes
        const gainsElement = document.getElementById('total-gains');
        const percentElement = document.getElementById('percentage-change');

        gainsElement.className = 'stat-value ' + (totalGains >= 0 ? 'positive' : 'negative');
        percentElement.className = 'stat-value ' + (percentageChange >= 0 ? 'positive' : 'negative');

        // Update portfolio chart
        this.updatePortfolioChart(holdings);
    }

    updateTransactionsTable() {
        const tbody = document.getElementById('transactions-tbody');

        if (this.data.transactions.length === 0) {
            tbody.innerHTML = '<tr class="no-data"><td colspan="8">No transactions loaded. Upload a file to get started.</td></tr>';
            return;
        }

        tbody.innerHTML = '';

        this.data.transactions.forEach((transaction, index) => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${this.formatDate(transaction.date)}</td>
                <td>${transaction.confirmationNo}</td>
                <td><span class="action-badge ${transaction.action.toLowerCase()}">${transaction.action}</span></td>
                <td>${transaction.quantity.toLocaleString()}</td>
                <td>${this.formatCurrency(transaction.avgPrice)}</td>
                <td>${this.formatCurrency(transaction.fees || 0)}</td>
                <td>${this.formatCurrency(transaction.settlValue || 0)}</td>
                <td>
                    <button class="btn-small btn-danger" onclick="wealthTracker.deleteTransaction(${index})">
                        Delete
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    updateHoldingsTable() {
        const tbody = document.getElementById('holdings-tbody');
        const holdings = Array.from(this.data.holdings.values());

        if (holdings.length === 0) {
            tbody.innerHTML = '<tr class="no-data"><td colspan="7">No holdings to display.</td></tr>';
            return;
        }

        tbody.innerHTML = '';

        holdings.forEach(holding => {
            const cgtStatus = this.calculateCGTStatus(holding);
            const row = document.createElement('tr');

            row.innerHTML = `
                <td><strong>${holding.code}</strong></td>
                <td>${holding.quantity.toLocaleString()}</td>
                <td>${this.formatCurrency(holding.avgCost)}</td>
                <td>${this.formatCurrency(holding.currentValue || 0)}</td>
                <td class="${holding.unrealizedPL >= 0 ? 'positive' : 'negative'}">
                    ${this.formatCurrency(holding.unrealizedPL || 0)}
                </td>
                <td class="${holding.percentChange >= 0 ? 'positive' : 'negative'}">
                    ${(holding.percentChange || 0).toFixed(2)}%
                </td>
                <td>
                    <span class="cgt-status ${cgtStatus.eligible ? 'eligible' : 'not-eligible'}">
                        ${cgtStatus.text}
                    </span>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    calculateCGTStatus(holding) {
        if (!holding.firstPurchaseDate) {
            return { eligible: false, text: 'No purchase date' };
        }

        const purchaseDate = new Date(holding.firstPurchaseDate);
        const now = new Date();
        const daysDiff = Math.floor((now - purchaseDate) / (1000 * 60 * 60 * 24));

        if (daysDiff >= 365) {
            return {
                eligible: true,
                text: `50% CGT discount (${Math.floor(daysDiff / 365)}y ${daysDiff % 365}d)`
            };
        } else {
            const daysRemaining = 365 - daysDiff;
            return {
                eligible: false,
                text: `${daysRemaining} days until 50% discount`
            };
        }
    }

    deleteTransaction(index) {
        if (confirm('Are you sure you want to delete this transaction?')) {
            this.data.transactions.splice(index, 1);
            this.calculateHoldings();
            this.saveData();
            this.updateTransactionsTable();
            this.updateHoldingsTable();
            this.updatePortfolioDisplay();
            this.showStatus('Transaction deleted successfully.', 'success');
        }
    }

    processTransactions(data) {
        try {
            const newTransactions = [];

            for (const row of data) {
                // Map common column variations to standard names
                const transaction = this.normalizeTransaction(row);

                if (this.validateTransaction(transaction)) {
                    // Add unique ID and processing date
                    transaction.id = this.generateTransactionId();
                    transaction.processedDate = new Date().toISOString();
                    newTransactions.push(transaction);
                }
            }

            // Add to existing transactions
            this.data.transactions.push(...newTransactions);

            // Update holdings
            this.calculateHoldings();

            // Save and update UI
            this.saveData();
            this.updateTransactionsTable();
            this.updateHoldingsTable();
            this.updatePortfolioDisplay();

            this.showStatus(`Successfully processed ${newTransactions.length} transactions.`, 'success');

        } catch (error) {
            console.error('Error processing transactions:', error);
            this.showStatus('Error processing transactions: ' + error.message, 'error');
        }
    }

    normalizeTransaction(row) {
        // Handle different column name variations
        const columnMappings = {
            'date': ['Date', 'Trade Date', 'Settlement Date', 'date'],
            'confirmationNo': ['Confirmation No.', 'Confirmation', 'Conf No', 'confirmationNo'],
            'code': ['Code', 'Symbol', 'Stock Code', 'Ticker', 'code'],
            'quantity': ['Quantity', 'Qty', 'Units', 'Shares', 'quantity'],
            'action': ['Action', 'Type', 'Transaction Type', 'Buy/Sell', 'action'],
            'avgPrice': ['Avg. price', 'Average Price', 'Price', 'Unit Price', 'avgPrice'],
            'fees': ['Fees', 'Brokerage', 'Commission', 'Costs', 'fees'],
            'settlValue': ['Settl. value', 'Settlement Value', 'Net Amount', 'Total', 'settlValue']
        };

        const normalized = {};

        for (const [standardKey, variations] of Object.entries(columnMappings)) {
            for (const variation of variations) {
                if (row[variation] !== undefined && row[variation] !== '') {
                    normalized[standardKey] = row[variation];
                    break;
                }
            }
        }

        // Clean and convert data types
        return {
            date: this.parseDate(normalized.date),
            confirmationNo: String(normalized.confirmationNo || '').trim(),
            code: String(normalized.code || '').trim().toUpperCase(),
            quantity: this.parseNumber(normalized.quantity),
            action: String(normalized.action || '').trim().toUpperCase(),
            avgPrice: this.parseNumber(normalized.avgPrice),
            fees: this.parseNumber(normalized.fees),
            settlValue: this.parseNumber(normalized.settlValue)
        };
    }

    validateTransaction(transaction) {
        const required = ['date', 'code', 'quantity', 'action', 'avgPrice'];

        for (const field of required) {
            if (!transaction[field] || transaction[field] === '' ||
                (typeof transaction[field] === 'number' && isNaN(transaction[field]))) {
                console.warn(`Invalid transaction - missing ${field}:`, transaction);
                return false;
            }
        }

        // Validate action type
        const validActions = ['BUY', 'SELL', 'PURCHASE', 'SALE'];
        if (!validActions.includes(transaction.action)) {
            console.warn(`Invalid action type: ${transaction.action}`);
            return false;
        }

        // Normalize action to BUY/SELL
        if (transaction.action === 'PURCHASE') transaction.action = 'BUY';
        if (transaction.action === 'SALE') transaction.action = 'SELL';

        return true;
    }

    calculateHoldings() {
        this.data.holdings.clear();

        // Sort transactions by date
        const sortedTransactions = [...this.data.transactions].sort((a, b) =>
            new Date(a.date) - new Date(b.date)
        );

        for (const transaction of sortedTransactions) {
            const code = transaction.code;

            if (!this.data.holdings.has(code)) {
                this.data.holdings.set(code, {
                    code: code,
                    quantity: 0,
                    totalCost: 0,
                    avgCost: 0,
                    transactions: [],
                    firstPurchaseDate: null
                });
            }

            const holding = this.data.holdings.get(code);
            holding.transactions.push(transaction);

            if (transaction.action === 'BUY') {
                const totalValue = transaction.quantity * transaction.avgPrice + (transaction.fees || 0);
                holding.totalCost += totalValue;
                holding.quantity += transaction.quantity;

                if (!holding.firstPurchaseDate) {
                    holding.firstPurchaseDate = transaction.date;
                }
            } else if (transaction.action === 'SELL') {
                // For sells, reduce quantity but keep cost basis proportional
                const sellRatio = transaction.quantity / holding.quantity;
                holding.totalCost *= (1 - sellRatio);
                holding.quantity -= transaction.quantity;
            }

            // Calculate average cost
            holding.avgCost = holding.quantity > 0 ? holding.totalCost / holding.quantity : 0;

            // Remove holdings with zero quantity
            if (holding.quantity <= 0) {
                this.data.holdings.delete(code);
            }
        }
    }

    generateTransactionId() {
        return 'txn_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    parseDate(dateStr) {
        if (!dateStr) return null;

        // Handle various date formats
        const date = new Date(dateStr);
        if (isNaN(date.getTime())) {
            // Try parsing DD/MM/YYYY format common in Australia
            const parts = dateStr.split('/');
            if (parts.length === 3) {
                const day = parseInt(parts[0]);
                const month = parseInt(parts[1]) - 1; // Month is 0-indexed
                const year = parseInt(parts[2]);
                return new Date(year, month, day).toISOString();
            }
            return null;
        }
        return date.toISOString();
    }

    parseNumber(value) {
        if (typeof value === 'number') return value;
        if (typeof value === 'string') {
            // Remove currency symbols, commas, and spaces
            const cleaned = value.replace(/[$,\s]/g, '');
            const num = parseFloat(cleaned);
            return isNaN(num) ? 0 : num;
        }
        return 0;
    }

    filterTransactions(searchTerm = '', actionFilter = '') {
        const tbody = document.getElementById('transactions-tbody');
        const rows = tbody.querySelectorAll('tr:not(.no-data)');

        let visibleCount = 0;

        rows.forEach(row => {
            const code = row.cells[1]?.textContent.toLowerCase() || '';
            const action = row.cells[2]?.textContent.toLowerCase() || '';

            const matchesSearch = !searchTerm || code.includes(searchTerm.toLowerCase());
            const matchesAction = !actionFilter || action === actionFilter.toLowerCase();

            if (matchesSearch && matchesAction) {
                row.style.display = '';
                visibleCount++;
            } else {
                row.style.display = 'none';
            }
        });

        // Show/hide no data message
        const noDataRow = tbody.querySelector('.no-data');
        if (noDataRow) {
            noDataRow.style.display = visibleCount === 0 && this.data.transactions.length > 0 ? '' : 'none';
        }
    }

    sortTable(column) {
        if (this.currentSort.column === column) {
            this.currentSort.direction = this.currentSort.direction === 'asc' ? 'desc' : 'asc';
        } else {
            this.currentSort.column = column;
            this.currentSort.direction = 'asc';
        }

        this.data.transactions.sort((a, b) => {
            let aVal = a[column];
            let bVal = b[column];

            // Handle different data types
            if (column === 'date') {
                aVal = new Date(aVal);
                bVal = new Date(bVal);
            } else if (['quantity', 'avgPrice', 'fees', 'settlValue'].includes(column)) {
                aVal = parseFloat(aVal) || 0;
                bVal = parseFloat(bVal) || 0;
            } else {
                aVal = String(aVal).toLowerCase();
                bVal = String(bVal).toLowerCase();
            }

            if (aVal < bVal) return this.currentSort.direction === 'asc' ? -1 : 1;
            if (aVal > bVal) return this.currentSort.direction === 'asc' ? 1 : -1;
            return 0;
        });

        this.updateTransactionsTable();

        // Update sort indicators
        document.querySelectorAll('th[data-sort]').forEach(th => {
            th.classList.remove('sort-asc', 'sort-desc');
        });

        const currentTh = document.querySelector(`th[data-sort="${column}"]`);
        if (currentTh) {
            currentTh.classList.add(`sort-${this.currentSort.direction}`);
        }
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.wealthTracker = new AustralianWealthTracker();
});
